namespace JT.Server.Entities;

/// <summary>
/// Represents a job transfer request with all associated data and preferences.
/// </summary>
public class TransferRequestData
{
	/// <summary>
	/// Gets or sets the unique identifier for the transfer request.
	/// </summary>
	public Guid Id { get; set; }

	/// <summary>
	/// Gets or sets the unique identifier of the user making the transfer request.
	/// </summary>
	public Guid UserId { get; set; }

	/// <summary>
	/// Gets or sets the title of the transfer request.
	/// </summary>
	public string? RequestTitle { get; set; }

	/// <summary>
	/// Gets or sets the current location of the employee.
	/// </summary>
	public LocationData? CurrentLocation { get; set; }

	/// <summary>
	/// Gets or sets the desired destination location for the transfer.
	/// </summary>
	public LocationData? DestinationLocation { get; set; }

	/// <summary>
	/// Gets or sets the current salary grade of the employee.
	/// </summary>
	public string? CurrentSalaryGrade { get; set; }

	/// <summary>
	/// Gets or sets the desired salary grade for the transfer.
	/// </summary>
	public string? DesiredSalaryGrade { get; set; }

	/// <summary>
	/// Gets or sets the current financial grade of the employee.
	/// </summary>
	public string? CurrentFinancialGrade { get; set; }

	/// <summary>
	/// Gets or sets the desired financial grade for the transfer.
	/// </summary>
	public string? DesiredFinancialGrade { get; set; }

	/// <summary>
	/// Gets or sets the industry sector for the transfer request.
	/// </summary>
	public string? Industry { get; set; }

	/// <summary>
	/// Gets or sets the time required to complete this transfer.
	/// </summary>
	[JsonConverter(typeof(TimeSpanObjectConverter))]
	public TimeSpan TransferTime { get; set; }

    /// <summary>
    /// Gets or sets the category data for the transfer request.
    /// </summary>
    public CategoryData? Category { get; set; }

    /// <summary>
    /// Gets or sets the reason for the transfer request.
    /// </summary>
	public string? TransferReason { get; set; }

	/// <summary>
	/// Gets or sets the current status of the transfer request.
	/// </summary>
	public string? Status { get; set; }

	/// <summary>
	/// Gets or sets the priority level of the transfer request.
	/// </summary>
	public string? Priority { get; set; }

	/// <summary>
	/// Gets or sets the date when the transfer request was submitted.
	/// </summary>
	public DateTime SubmissionDate { get; set; }

	/// <summary>
	/// Gets or sets the expiration date of the transfer request.
	/// </summary>
	public DateTime ExpirationDate { get; set; }

	/// <summary>
	/// Gets or sets the number of times this transfer request has been viewed.
	/// </summary>
	public int ViewCount { get; set; }

	/// <summary>
	/// Gets or sets the number of employers who have shown interest in this transfer request.
	/// </summary>
	public int InterestedEmployers { get; set; }

	/// <summary>
	/// Gets or sets the list of documents associated with the transfer request.
	/// </summary>
	public List<string>? Documents { get; set; }

	/// <summary>
	/// Gets or sets the list of required skills for the transfer position.
	/// </summary>
	public List<string>? RequiredSkills { get; set; }

	/// <summary>
	/// Gets or sets the preferred company size for the transfer.
	/// </summary>
	public string? PreferredCompanySize { get; set; }

	/// <summary>
	/// Gets or sets the remote work preference for the transfer position.
	/// </summary>
	public string? RemoteWorkPreference { get; set; }

	/// <summary>
	/// Gets or sets the list of language requirements for the transfer position.
	/// </summary>
	public List<string>? LanguageRequirements { get; set; }

	/// <summary>
	/// Gets or sets the user profile data of the person who created this transfer request.
	/// </summary>
	public UserProfileData? CreatedBy { get; set; }

	/// <summary>
	/// Gets or sets the list of employer responses to this transfer request.
	/// </summary>
	public List<EmployerResponseData>? Responses { get; set; }

	/// <summary>
	/// Gets or sets a value indicating whether this transfer request is marked as favorite.
	/// </summary>
	public bool? IsFavorite { get; set; }

	/// <summary>
	/// Gets or sets the nutrition data associated with the transfer request.
	/// </summary>
    public NutritionData? Nutrition { get; set; } = new(30, 101, 30, 110, 300, 75);
}

/// <summary>
/// Represents location data with city, state, and country information.
/// </summary>
public class LocationData
{
	/// <summary>
	/// Gets or sets the city name.
	/// </summary>
	public string? City { get; set; }

	/// <summary>
	/// Gets or sets the state or province name.
	/// </summary>
	public string? State { get; set; }

	/// <summary>
	/// Gets or sets the country name.
	/// </summary>
	public string? Country { get; set; }
}

/// <summary>
/// Represents user profile data including personal and professional information.
/// </summary>
public class UserProfileData
{
	/// <summary>
	/// Gets or sets the unique identifier for the user profile.
	/// </summary>
	public Guid Id { get; set; }

	/// <summary>
	/// Gets or sets the full name of the user.
	/// </summary>
	public string? FullName { get; set; }

	/// <summary>
	/// Gets or sets the URL of the user's profile image.
	/// </summary>
	public string? ProfileImageUrl { get; set; }

	/// <summary>
	/// Gets or sets the current position or job title of the user.
	/// </summary>
	public string? CurrentPosition { get; set; }

	/// <summary>
	/// Gets or sets the experience level or description of the user.
	/// </summary>
	public string? Experience { get; set; }
}

/// <summary>
/// Represents an employer's response to a transfer request.
/// </summary>
public class EmployerResponseData
{
	/// <summary>
	/// Gets or sets the unique identifier for the employer response.
	/// </summary>
	public string? Id { get; set; }

	/// <summary>
	/// Gets or sets the unique identifier of the employer.
	/// </summary>
	public string? EmployerId { get; set; }

	/// <summary>
	/// Gets or sets the name of the company.
	/// </summary>
	public string? CompanyName { get; set; }

	/// <summary>
	/// Gets or sets the date when the employer responded.
	/// </summary>
	public DateTime ResponseDate { get; set; }

	/// <summary>
	/// Gets or sets the status of the employer's response.
	/// </summary>
	public string? Status { get; set; }

	/// <summary>
	/// Gets or sets the message from the employer.
	/// </summary>
	public string? Message { get; set; }

	/// <summary>
	/// Gets or sets the salary offered by the employer.
	/// </summary>
	public string? OfferedSalary { get; set; }

	/// <summary>
	/// Gets or sets a value indicating whether an interview has been scheduled.
	/// </summary>
	public bool InterviewScheduled { get; set; }
}
