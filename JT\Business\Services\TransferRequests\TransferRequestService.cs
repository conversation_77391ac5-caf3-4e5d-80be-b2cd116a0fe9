using CommunityToolkit.Mvvm.Messaging;
using JT.Business.Models;
using JT.Business.Services.Users;
using JT.Content.Client;

namespace JT.Business.Services.TransferRequests;

public class TransferRequestService(JTApiClient api, IUserService userService, IMessenger messenger) : ITransferRequestService
{
	public async ValueTask<IImmutableList<TransferRequest>> GetAll(CancellationToken ct = default)
	{
		var transferRequestsData = await api.Api.TransferRequest.TransferRequest.GetAsync(cancellationToken: ct);
		return transferRequestsData?.Select(r => new TransferRequest(r)).ToImmutableList() ?? ImmutableList<TransferRequest>.Empty;
	}

	public async ValueTask<IImmutableList<TransferRequest>> GetByUser(Guid userId, CancellationToken ct = default)
	{
		var allRequests = await GetAll(ct);
		return allRequests.Where(r => r.UserId == userId).ToImmutableList();
	}

	public async ValueTask<TransferRequest?> GetById(Guid id, CancellationToken ct = default)
	{
		try
		{
			var transferRequestData = await api.Api.TransferRequest[id].GetAsync(cancellationToken: ct);
			return transferRequestData != null ? new TransferRequest(transferRequestData) : null;
		}
		catch
		{
			return null;
		}
	}

    public IListState<TransferRequest> FavoritedTransferRequests => ListState<TransferRequest>.Async(this, GetFavorited);

    public async ValueTask<IImmutableList<TransferRequest>> GetFavoritedWithPagination(uint pageSize, uint firstItemIndex, CancellationToken ct)
    {
        var favoritedTransferRequests = await GetFavorited(ct);
        return favoritedTransferRequests
            .Skip((int)firstItemIndex)
            .Take((int)pageSize)
            .ToImmutableList();
    }

    public async ValueTask Favorite(TransferRequest transferRequest, CancellationToken ct)
    {
        var currentUser = await userService.GetCurrent(ct);
        var updatedTransferRequest = transferRequest with { IsFavorite = !transferRequest.IsFavorite };

        // Note: This would typically be a POST to add/remove favorite, but the API structure suggests GET only
        // This is a placeholder implementation - the actual API call would depend on the server implementation

        if (updatedTransferRequest.IsFavorite)
        {
            await FavoritedTransferRequests.AddAsync(updatedTransferRequest, ct: ct);
        }
        else
        {
            await FavoritedTransferRequests.RemoveAllAsync(r => r.Id == updatedTransferRequest.Id, ct: ct);
        }

        // Note: EntityMessage and EntityChange types need to be defined
        // For now, commenting out the messaging until these types are created
        // messenger.Send(new EntityMessage<TransferRequest>(EntityChange.Updated, updatedTransferRequest));
    }
    public async ValueTask<IImmutableList<TransferRequest>> GetByCategory(int categoryId, CancellationToken ct)
    {
        var recipesData = await api.Api.TransferRequest.TransferRequest.GetAsync(cancellationToken: ct);
        return recipesData?.Where(r => r.JobCategory?.Id == categoryId).Select(r => new TransferRequest(r)).ToImmutableList() ?? ImmutableList<TransferRequest>.Empty;
    }

    public async ValueTask<IImmutableList<Category>> GetCategories(CancellationToken ct)
    {
        var categoriesData = await api.Api.JobCategory.Categories.GetAsync(cancellationToken: ct);
        return categoriesData?.Select(c => new Category(c)).ToImmutableList() ?? ImmutableList<Category>.Empty;
    }

    public async ValueTask<IImmutableList<CategoryWithCount>> GetCategoriesWithCount(CancellationToken ct)
    {
        var categories = await GetCategories(ct);
        var tasks = categories.Select(async category =>
        {
            var recipesByCategory = await GetByCategory(category.Id, ct);
            return new CategoryWithCount(recipesByCategory.Count, category);
        });

        var categoriesWithCount = await Task.WhenAll(tasks);
        return categoriesWithCount.ToImmutableList();
    }
    
    public async ValueTask<IImmutableList<TransferRequest>> GetByStatus(string status, CancellationToken ct = default)
	{
		var allRequests = await GetAll(ct);
		return allRequests.Where(r => string.Equals(r.Status, status, StringComparison.OrdinalIgnoreCase)).ToImmutableList();
	}

	public async ValueTask<IImmutableList<TransferRequest>> GetByLocation(string location, CancellationToken ct = default)
	{
		var allRequests = await GetAll(ct);
		return allRequests.Where(r => 
			r.DestinationLocation.City?.Contains(location, StringComparison.OrdinalIgnoreCase) == true ||
			r.DestinationLocation.State?.Contains(location, StringComparison.OrdinalIgnoreCase) == true ||
			r.DestinationLocation.Country?.Contains(location, StringComparison.OrdinalIgnoreCase) == true
		).ToImmutableList();
	}

	public async ValueTask<IImmutableList<TransferRequest>> GetByIndustry(string industry, CancellationToken ct = default)
	{
		var allRequests = await GetAll(ct);
		return allRequests.Where(r => string.Equals(r.Industry, industry, StringComparison.OrdinalIgnoreCase)).ToImmutableList();
	}

	public async ValueTask<IImmutableList<TransferRequest>> Search(string query, CancellationToken ct = default)
	{
		var allRequests = await GetAll(ct);
		return allRequests.Where(r =>
			r.RequestTitle?.Contains(query, StringComparison.OrdinalIgnoreCase) == true ||
			r.Industry?.Contains(query, StringComparison.OrdinalIgnoreCase) == true ||
			r.JobCategory?.Name?.Contains(query, StringComparison.OrdinalIgnoreCase) == true ||
			r.RequiredSkills.Any(skill => skill.Contains(query, StringComparison.OrdinalIgnoreCase))
		).ToImmutableList();
	}

	public async ValueTask<TransferRequest> Create(TransferRequest transferRequest, CancellationToken ct = default)
	{
		var transferRequestData = transferRequest.ToData();
		var createdData = await api.Api.TransferRequest.CreatetransferRequest.PostAsync(transferRequestData, cancellationToken: ct);
		return new TransferRequest(createdData ?? throw new InvalidOperationException("Failed to create transfer request"));
	}

	public async ValueTask<TransferRequest> Update(TransferRequest transferRequest, CancellationToken ct = default)
	{
		var transferRequestData = transferRequest.ToData();
		var updatedData = await api.Api.TransferRequest[transferRequest.Id].PutAsync(transferRequestData, cancellationToken: ct);
		return new TransferRequest(updatedData ?? throw new InvalidOperationException("Failed to update transfer request"));
	}

	public async ValueTask<bool> Delete(Guid id, CancellationToken ct = default)
	{
		try
		{
			await api.Api.TransferRequest[id].DeleteAsync(cancellationToken: ct);
			return true;
		}
		catch
		{
			return false;
		}
	}

	public async ValueTask<bool> UpdateStatus(Guid id, string status, CancellationToken ct = default)
	{
		try
		{
			var transferRequest = await GetById(id, ct);
			if (transferRequest == null) return false;

			var updatedRequest = transferRequest with { Status = status };
			await Update(updatedRequest, ct);
			return true;
		}
		catch
		{
			return false;
		}
	}

	public async ValueTask<bool> IncrementViewCount(Guid id, CancellationToken ct = default)
	{
		try
		{
			var transferRequest = await GetById(id, ct);
			if (transferRequest == null) return false;

			var updatedRequest = transferRequest with { ViewCount = transferRequest.ViewCount + 1 };
			await Update(updatedRequest, ct);
			return true;
		}
		catch
		{
			return false;
		}
	}

	public async ValueTask<IImmutableList<TransferRequest>> GetTrending(CancellationToken ct = default)
	{
		var allRequests = await GetAll(ct);
		return allRequests
			.Where(r => r.IsActive)
			.OrderByDescending(r => r.ViewCount)
			.ThenByDescending(r => r.InterestedEmployers)
			.Take(10)
			.ToImmutableList();
	}

	public async ValueTask<IImmutableList<TransferRequest>> GetExpiringSoon(CancellationToken ct = default)
	{
		var allRequests = await GetAll(ct);
		return allRequests
			.Where(r => r.IsActive && r.DaysUntilExpiration <= 7)
			.OrderBy(r => r.DaysUntilExpiration)
			.ToImmutableList();
	}

	//public async ValueTask<IImmutableList<Category>> GetCategories(CancellationToken ct = default)
	//{
	//	var categoriesData = await api.Api.JobCategory.Categories.GetAsync(cancellationToken: ct);
	//	return categoriesData?.Select(c => new Category(c)).ToImmutableList() ?? ImmutableList<Category>.Empty;
	//}

	private async ValueTask<IImmutableList<TransferRequest>> GetFavorited(CancellationToken ct)
	{
		var currentUser = await userService.GetCurrent(ct);
		var favoritedTransferRequestsData = await api.Api.TransferRequest.Favorited.GetAsync(config => config.QueryParameters.UserId = currentUser.Id, cancellationToken: ct);
		return favoritedTransferRequestsData?.Select(r => new TransferRequest(r)).ToImmutableList() ?? ImmutableList<TransferRequest>.Empty;
	}
}
