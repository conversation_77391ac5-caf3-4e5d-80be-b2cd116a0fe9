using JT.Business.Services.TransferRequests;
using JT.Business.Services.Users;

namespace JT.Presentation;

public partial record TransferRequestModel
{
	private readonly INavigator _navigator;
	private readonly ITransferRequestService _transferRequestService;
	private readonly IUserService _userService;
	private readonly IMessenger _messenger;

	public TransferRequestModel(
		INavigator navigator,
		ITransferRequestService transferRequestService,
		IUserService userService,
		IMessenger messenger)
	{
		_navigator = navigator;
		_transferRequestService = transferRequestService;
		_userService = userService;
		_messenger = messenger;
	}

	public string? TransferRequestId { get; set; }

	public IState<TransferRequest?> TransferRequest => State
		.Async(this, GetTransferRequest);

	public IFeed<User> CurrentUser => _userService.User;

	private async ValueTask<TransferRequest?> GetTransferRequest(CancellationToken ct)
	{
		if (string.IsNullOrEmpty(TransferRequestId) || !Guid.TryParse(TransferRequestId, out var id))
		{
			return null;
		}

		return await _transferRequestService.GetById(id, ct);
	}

	public async ValueTask ApplyForTransfer(CancellationToken ct)
	{
		// This would open an application form or contact the employer
		await _navigator.NavigateRouteAsync(this, route: "/Main/-/Apply", data: new { TransferRequestId }, cancellation: ct);
	}

	public async ValueTask ContactEmployer(string employerId, CancellationToken ct)
	{
		// This would open a messaging interface
		await _navigator.NavigateRouteAsync(this, route: "/Main/-/Messages", data: new { EmployerId = employerId }, cancellation: ct);
	}

	public ValueTask SaveTransferRequest(CancellationToken ct)
	{
		// Save to user's saved transfers
		// Implementation would depend on having a SavedTransfersService
		return ValueTask.CompletedTask;
	}

	public async ValueTask ShareTransferRequest(CancellationToken ct)
	{
		var transferRequest = await GetTransferRequest(ct);
		if (transferRequest != null)
		{
			// var shareText = $"Check out this job transfer opportunity: {transferRequest.RequestTitle} in {transferRequest.DestinationLocation.City}";
			// Use the existing share service
			// await _shareService.ShareText(shareText, ct);
		}
	}

	public async ValueTask ReportTransferRequest(CancellationToken ct)
	{
		// Report inappropriate content
		await _navigator.NavigateRouteAsync(this, route: "/Main/-/Report", data: new { TransferRequestId }, cancellation: ct);
	}

	public async ValueTask GoBack(CancellationToken ct) =>
		await _navigator.NavigateBackAsync(this, cancellation: ct);
}
