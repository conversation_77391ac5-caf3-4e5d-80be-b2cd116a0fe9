// <auto-generated/>
#pragma warning disable CS0618
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions.Serialization;
using System.Collections.Generic;
using System.IO;
using System;
namespace JT.Content.Client.Models
{
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    #pragma warning disable CS1591
    public partial class TransferRequestData : IParsable
    #pragma warning restore CS1591
    {
        /// <summary>The category property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public global::JT.Content.Client.Models.CategoryData? Category { get; set; }
#nullable restore
#else
        public global::JT.Content.Client.Models.CategoryData Category { get; set; }
#endif
        /// <summary>The createdBy property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public global::JT.Content.Client.Models.UserProfileData? CreatedBy { get; set; }
#nullable restore
#else
        public global::JT.Content.Client.Models.UserProfileData CreatedBy { get; set; }
#endif
        /// <summary>The currentFinancialGrade property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? CurrentFinancialGrade { get; set; }
#nullable restore
#else
        public string CurrentFinancialGrade { get; set; }
#endif
        /// <summary>The currentLocation property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public global::JT.Content.Client.Models.LocationData? CurrentLocation { get; set; }
#nullable restore
#else
        public global::JT.Content.Client.Models.LocationData CurrentLocation { get; set; }
#endif
        /// <summary>The currentSalaryGrade property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? CurrentSalaryGrade { get; set; }
#nullable restore
#else
        public string CurrentSalaryGrade { get; set; }
#endif
        /// <summary>The desiredFinancialGrade property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? DesiredFinancialGrade { get; set; }
#nullable restore
#else
        public string DesiredFinancialGrade { get; set; }
#endif
        /// <summary>The desiredSalaryGrade property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? DesiredSalaryGrade { get; set; }
#nullable restore
#else
        public string DesiredSalaryGrade { get; set; }
#endif
        /// <summary>The destinationLocation property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public global::JT.Content.Client.Models.LocationData? DestinationLocation { get; set; }
#nullable restore
#else
        public global::JT.Content.Client.Models.LocationData DestinationLocation { get; set; }
#endif
        /// <summary>The documents property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public List<string>? Documents { get; set; }
#nullable restore
#else
        public List<string> Documents { get; set; }
#endif
        /// <summary>The expirationDate property</summary>
        public DateTimeOffset? ExpirationDate { get; set; }
        /// <summary>The id property</summary>
        public Guid? Id { get; set; }
        /// <summary>The industry property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Industry { get; set; }
#nullable restore
#else
        public string Industry { get; set; }
#endif
        /// <summary>The interestedEmployers property</summary>
        public int? InterestedEmployers { get; set; }
        /// <summary>The isFavorite property</summary>
        public bool? IsFavorite { get; set; }
        /// <summary>The languageRequirements property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public List<string>? LanguageRequirements { get; set; }
#nullable restore
#else
        public List<string> LanguageRequirements { get; set; }
#endif
        /// <summary>The nutrition property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public global::JT.Content.Client.Models.NutritionData? Nutrition { get; set; }
#nullable restore
#else
        public global::JT.Content.Client.Models.NutritionData Nutrition { get; set; }
#endif
        /// <summary>The preferredCompanySize property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? PreferredCompanySize { get; set; }
#nullable restore
#else
        public string PreferredCompanySize { get; set; }
#endif
        /// <summary>The priority property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Priority { get; set; }
#nullable restore
#else
        public string Priority { get; set; }
#endif
        /// <summary>The remoteWorkPreference property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? RemoteWorkPreference { get; set; }
#nullable restore
#else
        public string RemoteWorkPreference { get; set; }
#endif
        /// <summary>The requestTitle property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? RequestTitle { get; set; }
#nullable restore
#else
        public string RequestTitle { get; set; }
#endif
        /// <summary>The requiredSkills property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public List<string>? RequiredSkills { get; set; }
#nullable restore
#else
        public List<string> RequiredSkills { get; set; }
#endif
        /// <summary>The responses property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public List<global::JT.Content.Client.Models.EmployerResponseData>? Responses { get; set; }
#nullable restore
#else
        public List<global::JT.Content.Client.Models.EmployerResponseData> Responses { get; set; }
#endif
        /// <summary>The status property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Status { get; set; }
#nullable restore
#else
        public string Status { get; set; }
#endif
        /// <summary>The submissionDate property</summary>
        public DateTimeOffset? SubmissionDate { get; set; }
        /// <summary>Duration in ISO 8601 format (e.g., PT1H30M for 1 hour 30 minutes) or custom TimeSpan format</summary>
        public TimeSpan? Transferime { get; set; }
        /// <summary>The transferReason property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? TransferReason { get; set; }
#nullable restore
#else
        public string TransferReason { get; set; }
#endif
        /// <summary>The userId property</summary>
        public Guid? UserId { get; set; }
        /// <summary>The viewCount property</summary>
        public int? ViewCount { get; set; }
        /// <summary>
        /// Creates a new instance of the appropriate class based on discriminator value
        /// </summary>
        /// <returns>A <see cref="global::JT.Content.Client.Models.TransferRequestData"/></returns>
        /// <param name="parseNode">The parse node to use to read the discriminator value and create the object</param>
        public static global::JT.Content.Client.Models.TransferRequestData CreateFromDiscriminatorValue(IParseNode parseNode)
        {
            _ = parseNode ?? throw new ArgumentNullException(nameof(parseNode));
            return new global::JT.Content.Client.Models.TransferRequestData();
        }
        /// <summary>
        /// The deserialization information for the current model
        /// </summary>
        /// <returns>A IDictionary&lt;string, Action&lt;IParseNode&gt;&gt;</returns>
        public virtual IDictionary<string, Action<IParseNode>> GetFieldDeserializers()
        {
            return new Dictionary<string, Action<IParseNode>>
            {
                { "category", n => { Category = n.GetObjectValue<global::JT.Content.Client.Models.CategoryData>(global::JT.Content.Client.Models.CategoryData.CreateFromDiscriminatorValue); } },
                { "createdBy", n => { CreatedBy = n.GetObjectValue<global::JT.Content.Client.Models.UserProfileData>(global::JT.Content.Client.Models.UserProfileData.CreateFromDiscriminatorValue); } },
                { "currentFinancialGrade", n => { CurrentFinancialGrade = n.GetStringValue(); } },
                { "currentLocation", n => { CurrentLocation = n.GetObjectValue<global::JT.Content.Client.Models.LocationData>(global::JT.Content.Client.Models.LocationData.CreateFromDiscriminatorValue); } },
                { "currentSalaryGrade", n => { CurrentSalaryGrade = n.GetStringValue(); } },
                { "desiredFinancialGrade", n => { DesiredFinancialGrade = n.GetStringValue(); } },
                { "desiredSalaryGrade", n => { DesiredSalaryGrade = n.GetStringValue(); } },
                { "destinationLocation", n => { DestinationLocation = n.GetObjectValue<global::JT.Content.Client.Models.LocationData>(global::JT.Content.Client.Models.LocationData.CreateFromDiscriminatorValue); } },
                { "documents", n => { Documents = n.GetCollectionOfPrimitiveValues<string>()?.AsList(); } },
                { "expirationDate", n => { ExpirationDate = n.GetDateTimeOffsetValue(); } },
                { "id", n => { Id = n.GetGuidValue(); } },
                { "industry", n => { Industry = n.GetStringValue(); } },
                { "interestedEmployers", n => { InterestedEmployers = n.GetIntValue(); } },
                { "isFavorite", n => { IsFavorite = n.GetBoolValue(); } },
                { "languageRequirements", n => { LanguageRequirements = n.GetCollectionOfPrimitiveValues<string>()?.AsList(); } },
                { "nutrition", n => { Nutrition = n.GetObjectValue<global::JT.Content.Client.Models.NutritionData>(global::JT.Content.Client.Models.NutritionData.CreateFromDiscriminatorValue); } },
                { "preferredCompanySize", n => { PreferredCompanySize = n.GetStringValue(); } },
                { "priority", n => { Priority = n.GetStringValue(); } },
                { "remoteWorkPreference", n => { RemoteWorkPreference = n.GetStringValue(); } },
                { "requestTitle", n => { RequestTitle = n.GetStringValue(); } },
                { "requiredSkills", n => { RequiredSkills = n.GetCollectionOfPrimitiveValues<string>()?.AsList(); } },
                { "responses", n => { Responses = n.GetCollectionOfObjectValues<global::JT.Content.Client.Models.EmployerResponseData>(global::JT.Content.Client.Models.EmployerResponseData.CreateFromDiscriminatorValue)?.AsList(); } },
                { "status", n => { Status = n.GetStringValue(); } },
                { "submissionDate", n => { SubmissionDate = n.GetDateTimeOffsetValue(); } },
                { "transferReason", n => { TransferReason = n.GetStringValue(); } },
                { "transferime", n => { Transferime = n.GetTimeSpanValue(); } },
                { "userId", n => { UserId = n.GetGuidValue(); } },
                { "viewCount", n => { ViewCount = n.GetIntValue(); } },
            };
        }
        /// <summary>
        /// Serializes information the current object
        /// </summary>
        /// <param name="writer">Serialization writer to use to serialize this model</param>
        public virtual void Serialize(ISerializationWriter writer)
        {
            _ = writer ?? throw new ArgumentNullException(nameof(writer));
            writer.WriteObjectValue<global::JT.Content.Client.Models.CategoryData>("category", Category);
            writer.WriteObjectValue<global::JT.Content.Client.Models.UserProfileData>("createdBy", CreatedBy);
            writer.WriteStringValue("currentFinancialGrade", CurrentFinancialGrade);
            writer.WriteObjectValue<global::JT.Content.Client.Models.LocationData>("currentLocation", CurrentLocation);
            writer.WriteStringValue("currentSalaryGrade", CurrentSalaryGrade);
            writer.WriteStringValue("desiredFinancialGrade", DesiredFinancialGrade);
            writer.WriteStringValue("desiredSalaryGrade", DesiredSalaryGrade);
            writer.WriteObjectValue<global::JT.Content.Client.Models.LocationData>("destinationLocation", DestinationLocation);
            writer.WriteCollectionOfPrimitiveValues<string>("documents", Documents);
            writer.WriteDateTimeOffsetValue("expirationDate", ExpirationDate);
            writer.WriteGuidValue("id", Id);
            writer.WriteStringValue("industry", Industry);
            writer.WriteIntValue("interestedEmployers", InterestedEmployers);
            writer.WriteBoolValue("isFavorite", IsFavorite);
            writer.WriteCollectionOfPrimitiveValues<string>("languageRequirements", LanguageRequirements);
            writer.WriteObjectValue<global::JT.Content.Client.Models.NutritionData>("nutrition", Nutrition);
            writer.WriteStringValue("preferredCompanySize", PreferredCompanySize);
            writer.WriteStringValue("priority", Priority);
            writer.WriteStringValue("remoteWorkPreference", RemoteWorkPreference);
            writer.WriteStringValue("requestTitle", RequestTitle);
            writer.WriteCollectionOfPrimitiveValues<string>("requiredSkills", RequiredSkills);
            writer.WriteCollectionOfObjectValues<global::JT.Content.Client.Models.EmployerResponseData>("responses", Responses);
            writer.WriteStringValue("status", Status);
            writer.WriteDateTimeOffsetValue("submissionDate", SubmissionDate);
            writer.WriteTimeSpanValue("transferime", Transferime);
            writer.WriteStringValue("transferReason", TransferReason);
            writer.WriteGuidValue("userId", UserId);
            writer.WriteIntValue("viewCount", ViewCount);
        }
    }
}
#pragma warning restore CS0618
