namespace JT.Content.Client.Mock;

public abstract class BaseMockEndpoint(ISerializer serializer, ILogger<BaseMockEndpoint> _logger)
{
	protected async Task<T?> LoadData<T>(string fileName)
	{
		try
		{
			var uri = new Uri($"ms-appx:///AppData/{fileName}");
			_logger.LogInformation("Attempting to load file from URI: {Uri}", uri);

			var file = await StorageFile.GetFileFromApplicationUriAsync(uri);
			var json = await FileIO.ReadTextAsync(file);

			_logger.LogInformation("Successfully loaded {FileName}, content length: {Length}", fileName, json.Length);

			var result = serializer.FromString<T>(json);
			_logger.LogInformation("Successfully deserialized {FileName} to type {Type}", fileName, typeof(T).Name);

			return result;
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Failed to load {FileName} from ms-appx:///AppData/{FileName}", fileName, fileName);
			return default;
		}
	}
}
