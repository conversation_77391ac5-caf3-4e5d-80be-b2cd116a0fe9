using CommunityToolkit.Mvvm.Messaging;
using JT.Business.Services.Subscriptions;
using JT.Business.Services.TransferRequests;
using JT.Business.Services.Users;

namespace JT.Presentation;

public partial record HomeModel
{
	private readonly INavigator _navigator;
    private readonly ITransferRequestService _transferRequestService;
    private readonly IUserService _userService;
    private readonly ISubscriptionService _subscriptionService;
    private readonly IMessenger _messenger;

    public HomeModel(
		INavigator navigator,
        ITransferRequestService transferRequestService,
        IUserService userService,
        ISubscriptionService subscriptionService,
        IMessenger messenger)
	{
		_navigator = navigator;
        _transferRequestService = transferRequestService;
        _userService = userService;
        _subscriptionService = subscriptionService;
        _messenger = messenger;
    }

    public IListState<TransferRequest> TrendingNow => ListState
    .Async(this, _transferRequestService.GetTrending)
    .Observe(_messenger, r => r.Id);

    public IListFeed<CategoryWithCount> Categories => ListFeed.Async(_transferRequestService.GetCategoriesWithCount);

    public IListFeed<TransferRequest> RecentlyAdded => ListFeed.Async(_transferRequestService.GetRecent);

    public IListFeed<User> PopularCreators => ListFeed.Async(_userService.GetPopularCreators);

    public IFeed<User> UserProfile => _userService.User;

    public async ValueTask ShowAll(CancellationToken ct) =>
        await _navigator.NavigateRouteAsync(this, route: "/Main/-/Search", data: new SearchFilter(FilterGroup: FilterGroup.Popular), cancellation: ct);

    public async ValueTask ShowAllRecentlyAdded(CancellationToken ct) =>
        await _navigator.NavigateRouteAsync(this, route: "/Main/-/Search", data: new SearchFilter(FilterGroup: FilterGroup.Recent), cancellation: ct);

    public async ValueTask CategorySearch(CategoryWithCount categoryWithCount, CancellationToken ct) =>
        await _navigator.NavigateRouteAsync(this, route: "/Main/-/Search", data: new SearchFilter(Category: categoryWithCount.Category), cancellation: ct);

    public async ValueTask FavoriteRecipe(TransferRequest transferRequest, CancellationToken ct) =>
        await _transferRequestService.Favorite(transferRequest, ct);





    public IListFeed<TransferRequest> TrendingTransfers => ListFeed.Async(_transferRequestService.GetTrending);

    public IListFeed<CategoryWithCount> Categories => ListFeed.Async(_transferRequestService.GetCategoriesWithCount);
    //public IListFeed<Category> JobCategories => ListFeed.Async(GetJobCategories);

    public IListFeed<TransferRequest> RecentTransfers => ListFeed.Async(_transferRequestService.GetAll);

    public IListFeed<User> ActiveUsers => ListFeed.Async(_userService.GetPopularCreators);

	public IFeed<User> UserProfile => _userService.User;

    public IFeed<SubscriptionPlan> CurrentSubscription => Feed.Async(async ct =>
    {
        var subscription = await GetCurrentSubscription(ct);
        return subscription ?? new SubscriptionPlan(new SubscriptionPlanData { Id = "free", Name = "Free Plan" });
    });

    public IState<int> TokenBalance => State.Async(this, GetTokenBalance);

    private async ValueTask<SubscriptionPlan?> GetCurrentSubscription(CancellationToken ct)
    {
        var user = await _userService.GetCurrent(ct);
        if (user?.SubscriptionTier != null)
        {
            return await _subscriptionService.GetPlanById(user.SubscriptionTier.Id?.ToLowerInvariant() ?? "", ct);
        }
        return null;
    }

    private async ValueTask<int> GetTokenBalance(CancellationToken ct)
	{
		var user = await _userService.GetCurrent(ct);
		return user?.TokenBalance ?? 0;
	}

	// Navigation methods
	public async ValueTask ShowAllTransfers(CancellationToken ct) =>
		await _navigator.NavigateRouteAsync(this, route: "/Main/-/Search", cancellation: ct);

	public async ValueTask ShowRecentTransfers(CancellationToken ct) =>
		await _navigator.NavigateRouteAsync(this, route: "/Main/-/Recent", cancellation: ct);

    public async ValueTask CategorySearch(Category jobCategory, CancellationToken ct) =>
        await _navigator.NavigateRouteAsync(this, route: "/Main/-/Search", data: new { Category = jobCategory }, cancellation: ct);

    public async ValueTask ViewTransferRequest(TransferRequest transferRequest, CancellationToken ct)
    {
        await _transferRequestService.IncrementViewCount(transferRequest.Id, ct);
        await _navigator.NavigateRouteAsync(this, route: $"/Main/-/TransferRequest/{transferRequest.Id}", cancellation: ct);
    }

    public async ValueTask CreateTransferRequest(CancellationToken ct) =>
		await _navigator.NavigateRouteAsync(this, route: "/Main/-/CreateTransfer", cancellation: ct);

	public async ValueTask ViewSubscriptions(CancellationToken ct) =>
		await _navigator.NavigateRouteAsync(this, route: "/Main/-/Subscriptions", cancellation: ct);

	public async ValueTask ViewTokens(CancellationToken ct) =>
		await _navigator.NavigateRouteAsync(this, route: "/Main/-/Tokens", cancellation: ct);
}
