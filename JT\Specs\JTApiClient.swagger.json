{"openapi": "3.0.4", "info": {"title": "JobTransfer API", "description": "API for Omani Job Transfer Application with subscription tiers, token system, and payment processing", "version": "v1"}, "servers": [{"url": "https://localhost:5002", "description": "Local development server"}], "paths": {"/api/JobCategory/categories": {"get": {"tags": ["JobCategory"], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryData"}}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/JobCategory/{id}": {"get": {"tags": ["JobCategory"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CategoryData"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/Notification": {"get": {"tags": ["Notification"], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/NotificationData"}}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/Skill": {"get": {"tags": ["Skill"], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SkillData"}}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/Skill/{id}": {"get": {"tags": ["Skill"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SkillData"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/Skill/category/{category}": {"get": {"tags": ["Skill"], "parameters": [{"name": "category", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SkillData"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/Skill/industry/{industry}": {"get": {"tags": ["Skill"], "parameters": [{"name": "industry", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SkillData"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/Skill/trending": {"get": {"tags": ["Skill"], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SkillData"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/Subscription/plans": {"get": {"tags": ["Subscription"], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SubscriptionPlanData"}}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/Subscription/plans/{planId}": {"get": {"tags": ["Subscription"], "parameters": [{"name": "planId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SubscriptionPlanData"}}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/Token/user/{userId}/transactions": {"get": {"tags": ["Token"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "type", "in": "query", "schema": {"type": "string"}}, {"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 20}}, {"name": "sortBy", "in": "query", "schema": {"type": "string", "default": "createdAt"}}, {"name": "sortOrder", "in": "query", "schema": {"type": "string", "default": "desc"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenTransactionDataPaginatedResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/Token/user/{userId}/balance": {"get": {"tags": ["Token"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenBalanceResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/Token/user/{userId}/add": {"post": {"tags": ["Token"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddTokensRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AddTokensRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AddTokensRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenTransactionData"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidationProblemDetails"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/Token/user/{userId}/deduct": {"post": {"tags": ["Token"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeductTokensRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeductTokensRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DeductTokensRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenTransactionData"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidationProblemDetails"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/Token/transactions/type/{type}": {"get": {"tags": ["Token"], "parameters": [{"name": "type", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 20}}, {"name": "sortBy", "in": "query", "schema": {"type": "string", "default": "createdAt"}}, {"name": "sortOrder", "in": "query", "schema": {"type": "string", "default": "desc"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenTransactionDataPaginatedResponse"}}}}}}}, "/api/TransferRequest/transferRequest": {"get": {"tags": ["TransferRequest"], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TransferRequestData"}}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/TransferRequest/{id}": {"get": {"tags": ["TransferRequest"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransferRequestData"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}, "put": {"tags": ["TransferRequest"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransferRequestData"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TransferRequestData"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TransferRequestData"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransferRequestData"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}, "delete": {"tags": ["TransferRequest"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransferRequestData"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}, "post": {"tags": ["TransferRequest"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransferRequestData"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/TransferRequest/createtransferRequest": {"post": {"tags": ["TransferRequest"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransferRequestData"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TransferRequestData"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TransferRequestData"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TransferRequestData"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TransferRequestData"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TransferRequestData"}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/TransferRequest/user/{userId}": {"get": {"tags": ["TransferRequest"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransferRequestData"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/TransferRequest/status/{status}": {"get": {"tags": ["TransferRequest"], "parameters": [{"name": "status", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransferRequestData"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/TransferRequest/favorited": {"get": {"tags": ["TransferRequest"], "parameters": [{"name": "userId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TransferRequestData"}}}}}}}}, "/api/User": {"get": {"tags": ["User"], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserData"}}}}}}}}, "/api/User/authenticate": {"post": {"tags": ["User"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "string", "format": "uuid"}}, "application/json": {"schema": {"type": "string", "format": "uuid"}}, "text/json": {"schema": {"type": "string", "format": "uuid"}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/User/popular-creators": {"get": {"tags": ["User"], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserData"}}}}}}}}, "/api/User/current": {"get": {"tags": ["User"], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserData"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/User/update": {"put": {"tags": ["User"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserData"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserData"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UserData"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserData"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/User/{id}": {"get": {"tags": ["User"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserData"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}}, "components": {"schemas": {"AddTokensRequest": {"required": ["amount", "description", "type"], "type": "object", "properties": {"amount": {"maximum": 10000, "minimum": 1, "type": "integer", "format": "int32"}, "type": {"maxLength": 50, "minLength": 0, "pattern": "^[a-zA-Z_]+$", "type": "string"}, "description": {"maxLength": 500, "minLength": 0, "type": "string"}, "referenceId": {"maxLength": 100, "minLength": 0, "type": "string", "nullable": true}}, "additionalProperties": false}, "CategoryData": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32", "nullable": true}, "urlIcon": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "color": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}}, "additionalProperties": false}, "DeductTokensRequest": {"required": ["amount", "description", "type"], "type": "object", "properties": {"amount": {"maximum": 10000, "minimum": 1, "type": "integer", "format": "int32"}, "type": {"maxLength": 50, "minLength": 0, "pattern": "^[a-zA-Z_]+$", "type": "string"}, "description": {"maxLength": 500, "minLength": 0, "type": "string"}, "referenceId": {"maxLength": 100, "minLength": 0, "type": "string", "nullable": true}}, "additionalProperties": false}, "EmployerResponseData": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "employerId": {"type": "string", "nullable": true}, "companyName": {"type": "string", "nullable": true}, "responseDate": {"type": "string", "format": "date-time"}, "status": {"type": "string", "nullable": true}, "message": {"type": "string", "nullable": true}, "offeredSalary": {"type": "string", "nullable": true}, "interviewScheduled": {"type": "boolean"}}, "additionalProperties": false}, "LocationData": {"type": "object", "properties": {"city": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "country": {"type": "string", "nullable": true}}, "additionalProperties": false}, "LoginRequest": {"type": "object", "properties": {"email": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}}, "additionalProperties": false}, "NotificationData": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "userId": {"type": "string", "format": "uuid", "nullable": true}, "title": {"type": "string", "nullable": true}, "body": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true}, "priority": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time", "nullable": true}, "date": {"type": "string", "format": "date-time", "nullable": true}, "read": {"type": "boolean", "nullable": true}, "isRead": {"type": "boolean", "nullable": true}, "readAt": {"type": "string", "format": "date-time", "nullable": true}, "actionUrl": {"type": "string", "nullable": true}, "imageUrl": {"type": "string", "nullable": true}}, "additionalProperties": false}, "NutritionData": {"type": "object", "properties": {"protein": {"type": "number", "format": "double", "readOnly": true}, "proteinBase": {"type": "number", "format": "double", "readOnly": true}, "carbs": {"type": "number", "format": "double", "readOnly": true}, "carbsBase": {"type": "number", "format": "double", "readOnly": true}, "fat": {"type": "number", "format": "double", "readOnly": true}, "fatBase": {"type": "number", "format": "double", "readOnly": true}}, "additionalProperties": false}, "ProblemDetails": {"type": "object", "properties": {"type": {"type": "string", "nullable": true}, "title": {"type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32", "nullable": true}, "detail": {"type": "string", "nullable": true}, "instance": {"type": "string", "nullable": true}}, "additionalProperties": {}}, "SkillData": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32", "nullable": true}, "name": {"type": "string", "nullable": true}, "category": {"type": "string", "nullable": true}, "popularityScore": {"type": "integer", "format": "int32", "nullable": true}, "industry": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SubscriptionPlanData": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "displayName": {"type": "string", "nullable": true}, "priceOMR": {"type": "number", "format": "double"}, "priceDisplay": {"type": "string", "nullable": true}, "duration": {"type": "string", "nullable": true}, "color": {"type": "string", "nullable": true}, "iconUrl": {"type": "string", "nullable": true}, "maxDestinations": {"type": "integer", "format": "int32"}, "priorityListing": {"type": "boolean"}, "priorityFrequency": {"type": "string", "nullable": true}, "whatsAppAlerts": {"type": "boolean"}, "emailAlerts": {"type": "boolean"}, "smsAlerts": {"type": "boolean"}, "inviteLimit": {"type": "integer", "format": "int32"}, "inviteLimitDisplay": {"type": "string", "nullable": true}, "tokenMultiplier": {"type": "number", "format": "double"}, "adFreeExperience": {"type": "boolean"}, "features": {"type": "array", "items": {"type": "string"}, "nullable": true}, "serviceFeePercentage": {"type": "number", "format": "double"}, "paymentGatewayFeePercentage": {"type": "number", "format": "double"}, "totalFeePercentage": {"type": "number", "format": "double"}, "isPopular": {"type": "boolean"}, "description": {"type": "string", "nullable": true}}, "additionalProperties": false}, "TokenBalanceResponse": {"type": "object", "properties": {"userId": {"type": "string", "format": "uuid"}, "balance": {"type": "integer", "format": "int32"}, "lastUpdated": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "TokenTransactionData": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "userId": {"type": "string", "format": "uuid"}, "amount": {"type": "integer", "format": "int32"}, "type": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "referenceId": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "balanceAfter": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "TokenTransactionDataPaginatedResponse": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/TokenTransactionData"}, "nullable": true}, "page": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalCount": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32"}, "hasPreviousPage": {"type": "boolean", "readOnly": true}, "hasNextPage": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "TransferRequestData": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "userId": {"type": "string", "format": "uuid"}, "requestTitle": {"type": "string", "nullable": true}, "currentLocation": {"$ref": "#/components/schemas/LocationData"}, "destinationLocation": {"$ref": "#/components/schemas/LocationData"}, "currentSalaryGrade": {"type": "string", "nullable": true}, "desiredSalaryGrade": {"type": "string", "nullable": true}, "currentFinancialGrade": {"type": "string", "nullable": true}, "desiredFinancialGrade": {"type": "string", "nullable": true}, "industry": {"type": "string", "nullable": true}, "transferTime": {"type": "string", "format": "date-span"}, "category": {"$ref": "#/components/schemas/CategoryData"}, "transferReason": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "priority": {"type": "string", "nullable": true}, "submissionDate": {"type": "string", "format": "date-time"}, "expirationDate": {"type": "string", "format": "date-time"}, "viewCount": {"type": "integer", "format": "int32"}, "interestedEmployers": {"type": "integer", "format": "int32"}, "documents": {"type": "array", "items": {"type": "string"}, "nullable": true}, "requiredSkills": {"type": "array", "items": {"type": "string"}, "nullable": true}, "preferredCompanySize": {"type": "string", "nullable": true}, "remoteWorkPreference": {"type": "string", "nullable": true}, "languageRequirements": {"type": "array", "items": {"type": "string"}, "nullable": true}, "createdBy": {"$ref": "#/components/schemas/UserProfileData"}, "responses": {"type": "array", "items": {"$ref": "#/components/schemas/EmployerResponseData"}, "nullable": true}, "isFavorite": {"type": "boolean", "nullable": true}, "nutrition": {"$ref": "#/components/schemas/NutritionData"}}, "additionalProperties": false}, "UserData": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "urlProfileImage": {"type": "string", "nullable": true}, "firstName": {"type": "string", "nullable": true}, "secondName": {"type": "string", "nullable": true}, "tribe": {"type": "string", "nullable": true}, "fullName": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "phoneNumber": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}, "age": {"type": "integer", "format": "int32", "nullable": true}, "gender": {"type": "string", "nullable": true}, "currentEmployerLocation": {"type": "string", "nullable": true}, "currentEmployerCity": {"type": "string", "nullable": true}, "currentEmployerState": {"type": "string", "nullable": true}, "currentSalaryGrade": {"type": "string", "nullable": true}, "educationalQualification": {"type": "string", "nullable": true}, "subscriptionTier": {"$ref": "#/components/schemas/SubscriptionPlanData"}, "tokenBalance": {"type": "integer", "format": "int32", "nullable": true}, "profileImageUrl": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "joinDate": {"type": "string", "format": "date-time", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}, "referralCode": {"type": "string", "nullable": true}, "invitedFriends": {"type": "integer", "format": "int32", "nullable": true}, "completedTransfers": {"type": "integer", "format": "int32", "nullable": true}, "isCurrent": {"type": "boolean"}, "currentEmployer": {"type": "string", "nullable": true}, "currentPosition": {"type": "string", "nullable": true}, "yearsOfExperience": {"type": "integer", "format": "int32", "nullable": true}, "skills": {"type": "array", "items": {"type": "string"}, "nullable": true}, "languages": {"type": "array", "items": {"type": "string"}, "nullable": true}, "certifications": {"type": "array", "items": {"type": "string"}, "nullable": true}, "preferredWorkType": {"type": "string", "nullable": true}, "isAvailableForTransfer": {"type": "boolean", "nullable": true}, "linkedInProfile": {"type": "string", "nullable": true}, "portfolio": {"type": "string", "nullable": true}, "resumeUrl": {"type": "string", "nullable": true}, "bio": {"type": "string", "nullable": true}, "lastLoginAt": {"type": "string", "format": "date-time", "nullable": true}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "UserProfileData": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "fullName": {"type": "string", "nullable": true}, "profileImageUrl": {"type": "string", "nullable": true}, "currentPosition": {"type": "string", "nullable": true}, "experience": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ValidationProblemDetails": {"type": "object", "properties": {"type": {"type": "string", "nullable": true}, "title": {"type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32", "nullable": true}, "detail": {"type": "string", "nullable": true}, "instance": {"type": "string", "nullable": true}, "errors": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}, "nullable": true}}, "additionalProperties": {}}}}}