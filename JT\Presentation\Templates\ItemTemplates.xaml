﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
					xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
					xmlns:utu="using:Uno.Toolkit.UI"
					xmlns:uer="using:Uno.Extensions.Reactive.UI">

	<DataTemplate x:Key="ReviewDataTemplate">
		<utu:AutoLayout Background="{ThemeResource SurfaceBrush}"
						CornerRadius="15"
						Margin="0,8">
			<utu:AutoLayout Spacing="16"
							Orientation="Horizontal">
				<PersonPicture utu:AutoLayout.CounterAlignment="Start"
							   Width="60"
							   Height="60"
							   ProfilePicture="{Binding UrlAuthorImage}"
							   Margin="16,18,0,18" />
				<utu:AutoLayout utu:AutoLayout.CounterAlignment="Start"
								utu:AutoLayout.PrimaryAlignment="Stretch"
								Padding="0,18,16,18"
								Spacing="16">
					<utu:AutoLayout PrimaryAxisAlignment="Center">
						<TextBlock TextWrapping="Wrap"
								   Text="{Binding Date, Converter={StaticResource StringFormatter}, ConverterParameter='{}{0:MMM dd, yyy}'}"
								   Foreground="{ThemeResource OnSurfaceVariantBrush}"
								   Style="{StaticResource CaptionSmall}" />
						<TextBlock TextWrapping="Wrap"
								   Text="{Binding PublisherName}"
								   Foreground="{ThemeResource SurfaceInverseBrush}"
								   Style="{StaticResource TitleMedium}" />
						<TextBlock TextWrapping="Wrap"
								   Text="{Binding Description}"
								   Foreground="{ThemeResource OnSurfaceVariantBrush}"
								   Style="{StaticResource LabelSmall}" />
					</utu:AutoLayout>
					<utu:AutoLayout Spacing="8"
									Orientation="Horizontal">
						<Button Background="{Binding UserLike, Converter={StaticResource UserLikeColorConverter}}"
								Content="{Binding Likes.Count, Converter={StaticResource StringFormatter}, ConverterParameter='👍 {0}'}"
								Command="{utu:AncestorBinding AncestorType=uer:FeedView,
															  Path=DataContext.Like}"
								CommandParameter="{Binding}"
								utu:AutoLayout.CounterAlignment="Start" />
						<Button Background="{Binding UserLike, Converter={StaticResource UserDislikeColorConverter}}"
								Content="{Binding Dislikes.Count, Converter={StaticResource StringFormatter}, ConverterParameter='👎 {0}'}"
								Command="{utu:AncestorBinding AncestorType=uer:FeedView,
															  Path=DataContext.Dislike}"
								CommandParameter="{Binding}"
								utu:AutoLayout.CounterAlignment="Start" />
					</utu:AutoLayout>
				</utu:AutoLayout>
			</utu:AutoLayout>
		</utu:AutoLayout>
	</DataTemplate>

    <DataTemplate x:Key="TransferRequestTemplate">
        <utu:CardContentControl x:Name="TransferRequestTemplateCard"
									HorizontalAlignment="Stretch"
									VerticalAlignment="Stretch"
									Background="{ThemeResource SurfaceBrush}"
									Style="{StaticResource FilledCardContentControlStyle}">
            <utu:CardContentControl.ContentTemplate>
                <DataTemplate>
                    <utu:AutoLayout HorizontalAlignment="Stretch"
										Background="{ThemeResource SurfaceBrush}"
										CornerRadius="4"
										PrimaryAxisAlignment="Center">
                        <Border Height="144" Background="{ThemeResource SurfaceVariantBrush}">
                            <utu:AutoLayout PrimaryAxisAlignment="Center" CounterAxisAlignment="Center">
                                <TextBlock Text="💼" FontSize="32" HorizontalAlignment="Center" />
                                <TextBlock Text="{Binding Status}"
											   HorizontalAlignment="Center"
											   Foreground="{ThemeResource OnSurfaceVariantBrush}"
											   Style="{StaticResource CaptionSmall}" />
                            </utu:AutoLayout>
                        </Border>
                        <utu:AutoLayout PrimaryAxisAlignment="Center"
											CounterAxisAlignment="Start"
											Padding="16">
                            <TextBlock Foreground="{ThemeResource OnSurfaceBrush}"
										   Style="{StaticResource TitleSmall}"
										   Text="{Binding RequestTitle}"
										   TextWrapping="Wrap" />
                            <TextBlock Foreground="{ThemeResource OnSurfaceMediumBrush}"
										   Style="{StaticResource CaptionMedium}"
										   Text="{Binding Industry}"
										   TextWrapping="Wrap" />
                        </utu:AutoLayout>
                    </utu:AutoLayout>
                </DataTemplate>
            </utu:CardContentControl.ContentTemplate>
        </utu:CardContentControl>
    </DataTemplate>
</ResourceDictionary>
