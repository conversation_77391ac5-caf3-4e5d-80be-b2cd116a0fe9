namespace JT.Server.Entities;

/// <summary>
/// Represents a single step in a recipe or process with associated data such as ingredients, cookware, and instructions.
/// </summary>
public class StepData
{
	/// <summary>
	/// Gets or sets the URL to a video demonstrating this step.
	/// </summary>
	public string? UrlVideo { get; set; }

	/// <summary>
	/// Gets or sets the name or title of this step.
	/// </summary>
	public string? Name { get; set; }

	/// <summary>
	/// Gets or sets the sequential number of this step in the process.
	/// </summary>
	public int Number { get; set; }

	/// <summary>
	/// Gets or sets the time required to complete this step.
	/// </summary>
	[JsonConverter(typeof(TimeSpanObjectConverter))]
	public TimeSpan TransferTime { get; set; }

	/// <summary>
	/// Gets or sets the list of cookware or tools required for this step.
	/// </summary>
	public List<string>? Cookware { get; set; }

	/// <summary>
	/// Gets or sets the list of ingredients used in this step.
	/// </summary>
	public List<string>? Ingredients { get; set; }

	/// <summary>
	/// Gets or sets the detailed description or instructions for this step.
	/// </summary>
	public string? Description { get; set; }
}
