namespace JT.Business.Services.TransferRequests;

public interface ITransferRequestService
{
	ValueTask<IImmutableList<TransferRequest>> GetAll(CancellationToken ct = default);
	ValueTask<IImmutableList<TransferRequest>> GetByUser(Guid userId, CancellationToken ct = default);
	ValueTask<TransferRequest?> GetById(Guid id, CancellationToken ct = default);
	ValueTask<IImmutableList<TransferRequest>> GetByStatus(string status, CancellationToken ct = default);
	ValueTask<IImmutableList<TransferRequest>> GetByLocation(string location, CancellationToken ct = default);
	ValueTask<IImmutableList<TransferRequest>> GetByIndustry(string industry, CancellationToken ct = default);
	ValueTask<IImmutableList<TransferRequest>> Search(string query, CancellationToken ct = default);
	ValueTask<TransferRequest> Create(TransferRequest transferRequest, CancellationToken ct = default);
	ValueTask<TransferRequest> Update(TransferRequest transferRequest, CancellationToken ct = default);
	ValueTask<bool> Delete(Guid id, CancellationToken ct = default);
	ValueTask<bool> UpdateStatus(Guid id, string status, CancellationToken ct = default);
	ValueTask<bool> IncrementViewCount(Guid id, CancellationToken ct = default);
	ValueTask<IImmutableList<TransferRequest>> GetTrending(CancellationToken ct = default);
	ValueTask<IImmutableList<TransferRequest>> GetExpiringSoon(CancellationToken ct = default);
    ValueTask<IImmutableList<TransferRequest>> GetByCategory(int categoryId, CancellationToken ct);
    ValueTask<IImmutableList<Category>> GetCategories(CancellationToken ct = default);
    ValueTask<IImmutableList<CategoryWithCount>> GetCategoriesWithCount(CancellationToken ct);
}
