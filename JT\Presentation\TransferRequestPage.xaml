﻿<Page x:Class="JT.Presentation.TransferRequestPage"
	  xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
	  xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
	  xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
	  xmlns:local="using:JT.Presentation"
	  xmlns:ctrl="using:JT.Presentation.Controls"
	  xmlns:lvc="using:LiveChartsCore.SkiaSharpView.WinUI"
	  xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
	  xmlns:muxc="using:Microsoft.UI.Xaml.Controls"
	  xmlns:not_mobile="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
	  xmlns:toolkit="using:Uno.UI.Toolkit"
	  xmlns:uen="using:Uno.Extensions.Navigation.UI"
	  xmlns:uer="using:Uno.Extensions.Reactive.UI"
	  xmlns:ut="using:Uno.Themes"
	  xmlns:converters="using:JT.Converters"
	  xmlns:utu="using:Uno.Toolkit.UI"
	  HorizontalAlignment="Stretch"
	  VerticalAlignment="Stretch"
	  NavigationCacheMode="Enabled"
	  mc:Ignorable="d"
	  Background="{ThemeResource BackgroundBrush}">

	<Page.Resources>
		<converters:BoolToObjectConverter x:Key="BoolToHeartIconConverter"
										  TrueValue="{StaticResource Icon_Heart_Filled}"
										  FalseValue="{StaticResource Icon_Heart}" />

		<DataTemplate x:Key="EmptyTemplate">
			<utu:AutoLayout Padding="{utu:Responsive Normal='16,24',
													 Wide='40'}"
							PrimaryAxisAlignment="Center"
							Spacing="24">
				<BitmapIcon utu:AutoLayout.CounterAlignment="Center"
							Width="72"
							Height="72"
							UriSource="{ThemeResource Empty_Recipe}" />
				<TextBlock Foreground="{ThemeResource OnSurfaceBrush}"
						   Style="{StaticResource TitleLarge}"
						   Text="No Reviews Yet"
						   TextAlignment="Center"
						   TextWrapping="Wrap" />
			</utu:AutoLayout>
		</DataTemplate>
	</Page.Resources>

	<utu:AutoLayout>
		<utu:NavigationBar Content="{Binding Recipe.Name}">
			<utu:NavigationBar.PrimaryCommands>
				<AppBarButton Command="{Binding Share}">
					<AppBarButton.Icon>
						<PathIcon Data="{StaticResource Icon_Share}" />
					</AppBarButton.Icon>
				</AppBarButton>
				<AppBarButton Command="{Binding Favorite}">
					<AppBarButton.Icon>
						<PathIcon Data="{Binding IsFavorited, Converter={StaticResource BoolToHeartIconConverter}, FallbackValue={StaticResource Icon_Heart}, TargetNullValue={StaticResource Icon_Heart}}" />
					</AppBarButton.Icon>
				</AppBarButton>
			</utu:NavigationBar.PrimaryCommands>
		</utu:NavigationBar>

		<ScrollViewer utu:AutoLayout.PrimaryAlignment="Stretch"
					  HorizontalScrollMode="Disabled"
					  VerticalScrollBarVisibility="{utu:Responsive Normal=Visible,
																   Wide=Hidden}"
					  VerticalScrollMode="{utu:Responsive Normal=Enabled,
														  Wide=Disabled}">
			<utu:AutoLayout Padding="{utu:Responsive Normal='0,0,0,92', Wide=0}">
				<utu:AutoLayout Spacing="8"
								Orientation="Horizontal"
								Padding="16"
								CounterAxisAlignment="Center">
					<PersonPicture ProfilePicture="{Binding User.UrlProfileImage}"
								   Width="32"
								   Height="32" />
					<TextBlock Text="{Binding User.FullName, Converter={StaticResource StringFormatter}, ConverterParameter='By {0}'}"
							   Foreground="{ThemeResource OnBackgroundMediumBrush}"
							   Style="{StaticResource BodySmall}" />
				</utu:AutoLayout>
				<utu:AutoLayout Orientation="{utu:Responsive Normal=Vertical,
															 Wide=Horizontal}"
								Justify="{utu:Responsive Normal=SpaceBetween,
														 Wide=Stack}"
								utu:AutoLayout.PrimaryAlignment="{utu:Responsive Normal=Auto,
																				 Wide=Stretch}">
					<utu:AutoLayout utu:AutoLayout.PrimaryAlignment="{utu:Responsive Normal=Auto,
																					 Wide=Stretch}"
									Spacing="{utu:Responsive Normal=8,
															 Wide=0}">
						<Image Visibility="{utu:Responsive Normal=Collapsed,
														   Wide=Visible}"
							   Source="{Binding Recipe.ImageUrl}"
							   Stretch="UniformToFill"
							   HorizontalAlignment="Center"
							   VerticalAlignment="Center"
							   utu:AutoLayout.PrimaryAlignment="Stretch" />
						<Border Visibility="{utu:Responsive Normal=Visible,
															Wide=Collapsed}"
								Height="300">
							<Image HorizontalAlignment="Center"
								   VerticalAlignment="Center"
								   Source="{Binding Recipe.ImageUrl}"
								   Stretch="UniformToFill" />
						</Border>
						<utu:AutoLayout utu:AutoLayout.IsIndependentLayout="True"
										Margin="{utu:Responsive Normal='16,263,16,0',
																Wide='14,14,0,0'}"
										VerticalAlignment="{utu:Responsive Normal=Center,
																		   Wide=Top}"
										HorizontalAlignment="{utu:Responsive Normal=Center,
																			 Wide=Left}"
										CornerRadius="4">
							<utu:AutoLayout Background="{ThemeResource SurfaceBrush}"
											Orientation="{utu:Responsive Normal=Horizontal,
																		 Wide=Vertical}"
											PrimaryAxisAlignment="Stretch"
											Width="{utu:Responsive Normal=358,
																   Wide=98}"
											Height="{utu:Responsive Normal=74,
																	Wide=317}"
											Padding="{utu:Responsive Normal='0,4',
																	 Wide='0,40'}"
											VerticalAlignment="Stretch"
											HorizontalAlignment="Stretch">
								<utu:AutoLayout Spacing="4"
												PrimaryAxisAlignment="Center"
												CounterAxisAlignment="Center"
												utu:AutoLayout.PrimaryAlignment="Stretch">
									<PathIcon Data="{StaticResource Icon_Timer}"
											  Foreground="{ThemeResource OnSurfaceBrush}" />
									<TextBlock TextAlignment="Center"
											   TextWrapping="Wrap"
											   Text="{Binding Recipe.CookTime, Converter={StaticResource TimeSpanToStringConverter}}"
											   Foreground="{ThemeResource OnSurfaceMediumBrush}"
											   Style="{StaticResource BodySmall}" />
								</utu:AutoLayout>
								<utu:AutoLayout Spacing="4"
												PrimaryAxisAlignment="Center"
												CounterAxisAlignment="Center"
												utu:AutoLayout.PrimaryAlignment="Stretch">
									<PathIcon Data="{StaticResource Icon_Star_Outline}"
											  Foreground="{ThemeResource OnSurfaceBrush}" />
									<TextBlock Foreground="{ThemeResource OnSurfaceMediumBrush}"
											   Style="{StaticResource BodySmall}"
											   Text="{Binding Recipe.Difficulty}"
											   TextAlignment="Center"
											   TextWrapping="Wrap" />
								</utu:AutoLayout>
								<utu:AutoLayout Spacing="4"
												PrimaryAxisAlignment="Center"
												CounterAxisAlignment="Center"
												utu:AutoLayout.PrimaryAlignment="Stretch">
									<PathIcon Data="{StaticResource Icon_Fire}"
											  Foreground="{ThemeResource OnSurfaceBrush}" />
									<TextBlock Foreground="{ThemeResource OnSurfaceMediumBrush}"
											   Style="{StaticResource BodySmall}"
											   Text="{Binding Recipe.Calories}"
											   TextAlignment="Center"
											   TextWrapping="Wrap" />
								</utu:AutoLayout>
							</utu:AutoLayout>
						</utu:AutoLayout>
					</utu:AutoLayout>
					<utu:AutoLayout x:Name="TabsLayout"
									uen:Region.Attached="True"
									Background="{ThemeResource BackgroundBrush}"
									utu:AutoLayout.PrimaryAlignment="Stretch">
						<utu:TabBar uen:Region.Attached="True"
									utu:AutoLayout.CounterAlignment="{utu:Responsive Normal=Start,
																					 Wide=Center}"
									Margin="{utu:Responsive Normal=0,
															Wide='40,0'}"
									Background="{ThemeResource BackgroundBrush}"
									Style="{StaticResource TopTabBarStyle}">
							<utu:TabBarItem uen:Region.Name="IngredientsTab"
											Content="Ingredients"
											IsSelected="True" />
							<utu:TabBarItem uen:Region.Name="StepsTab"
											Content="Steps"
											Foreground="{ThemeResource OnSurfaceMediumBrush}" />
							<utu:TabBarItem uen:Region.Name="ReviewsTab"
											Content="Reviews"
											Foreground="{ThemeResource OnSurfaceMediumBrush}" />
							<utu:TabBarItem uen:Region.Name="NutritionTab"
											Content="Nutrition"
											Foreground="{ThemeResource OnSurfaceMediumBrush}" />
						</utu:TabBar>
						<ScrollViewer utu:AutoLayout.PrimaryAlignment="{utu:Responsive Normal=Auto,
																					   Wide=Stretch}"
									  HorizontalScrollMode="Disabled">
							<Grid x:Name="TabNavGrid"
								  uen:Region.Attached="True"
								  uen:Region.Navigator="Visibility">
								<utu:AutoLayout Spacing="{utu:Responsive Normal=8,
																		 Wide=16}"
												Padding="{utu:Responsive Normal='16,24,16,16',
																		 Wide=40}"
												uen:Region.Name="IngredientsTab">
									<TextBlock Style="{StaticResource BodyLarge}"
											   Foreground="{ThemeResource OnSurfaceBrush}"
											   Text="{Binding Ingredients.Count, Converter={StaticResource StringFormatter}, ConverterParameter='{}{0} items'}"
											   TextWrapping="Wrap" />
									<utu:AutoLayout Spacing="{utu:Responsive Normal=2, Wide=0}">
										<uer:FeedView x:Name="IngredientsFeed"
													  Source="{Binding Ingredients}">
											<DataTemplate>
												<muxc:ItemsRepeater utu:AutoLayout.PrimaryAlignment="{utu:Responsive Normal=Stretch,
																													 Wide=Auto}"
																	ItemsSource="{Binding Data}">
													<muxc:ItemsRepeater.Layout>
														<muxc:StackLayout Orientation="Vertical"
																		  Spacing="2" />
													</muxc:ItemsRepeater.Layout>
													<muxc:ItemsRepeater.ItemTemplate>
														<DataTemplate>
															<utu:AutoLayout Spacing="16"
																			Height="60"
																			Background="{ThemeResource SurfaceBrush}"
																			CornerRadius="4"
																			Orientation="Horizontal"
																			CounterAxisAlignment="Center"
																			Justify="SpaceBetween"
																			Padding="8,0,16,0">
																<utu:AutoLayout Orientation="Horizontal"
																				CounterAxisAlignment="Center">
																	<TextBlock TextWrapping="Wrap"
																			   Text="{Binding Name}"
																			   Padding="16"
																			   Foreground="{ThemeResource OnSurfaceBrush}"
																			   Style="{StaticResource TitleMedium}" />
																</utu:AutoLayout>

																<Border Background="{ThemeResource BackgroundBrush}"
																		Padding="8"
																		CornerRadius="4">
																	<TextBlock TextAlignment="End"
																			   Text="{Binding Quantity}"
																			   Foreground="{ThemeResource OnSecondaryContainerBrush}"
																			   Style="{StaticResource BodySmall}" />
																</Border>
															</utu:AutoLayout>
														</DataTemplate>
													</muxc:ItemsRepeater.ItemTemplate>
												</muxc:ItemsRepeater>
											</DataTemplate>
										</uer:FeedView>
									</utu:AutoLayout>
								</utu:AutoLayout>

								<utu:AutoLayout Spacing="{utu:Responsive Normal=8,
																		 Wide=16}"
												Padding="{utu:Responsive Normal='16,24',
																		 Wide=40}"
												uen:Region.Name="StepsTab"
												Visibility="Collapsed">
									<TextBlock Style="{StaticResource BodyLarge}"
											   Foreground="{ThemeResource OnSurfaceBrush}"
											   Text="{Binding Steps.Count, Converter={StaticResource StringFormatter}, ConverterParameter='{}{0} steps'}"
											   TextWrapping="Wrap" />
									<uer:FeedView x:Name="StepsFeed"
												  Source="{Binding Steps}">
										<DataTemplate>
											<muxc:ItemsRepeater ItemsSource="{Binding Data}">
												<muxc:ItemsRepeater.Layout>
													<muxc:StackLayout Orientation="Vertical"
																	  Spacing="2" />
												</muxc:ItemsRepeater.Layout>
												<muxc:ItemsRepeater.ItemTemplate>
													<DataTemplate>
														<Border Background="{ThemeResource SurfaceBrush}"
																CornerRadius="4">
															<utu:AutoLayout Spacing="8"
																			PrimaryAxisAlignment="Center"
																			Padding="16">
																<TextBlock TextWrapping="Wrap"
																		   Text="{Binding Name}"
																		   Foreground="{ThemeResource OnSurfaceBrush}"
																		   Style="{StaticResource TitleSmall}" />
																<TextBlock TextWrapping="Wrap"
																		   Text="{Binding Description}"
																		   Foreground="{ThemeResource OnSurfaceMediumBrush}" />
															</utu:AutoLayout>
														</Border>
													</DataTemplate>
												</muxc:ItemsRepeater.ItemTemplate>
											</muxc:ItemsRepeater>
										</DataTemplate>
									</uer:FeedView>
								</utu:AutoLayout>

								<utu:AutoLayout Spacing="{utu:Responsive Normal=8,
																		 Wide=16}"
												Padding="{utu:Responsive Normal='16,24',
																		 Wide=40}"
												uen:Region.Name="ReviewsTab"
												Visibility="Collapsed">
									<TextBlock Style="{StaticResource BodyLarge}"
											   Foreground="{ThemeResource OnSurfaceBrush}"
											   Text="{Binding Reviews.Count, Converter={StaticResource StringFormatter}, ConverterParameter='{}{0} comments'}"
											   TextWrapping="Wrap"
											   Visibility="{Binding Reviews.Count, Converter={StaticResource GreaterThanZeroToVisibleConverter}}" />
									<uer:FeedView x:Name="ReviewsFeed"
												  VerticalContentAlignment="Center"
												  NoneTemplate="{StaticResource EmptyTemplate}"
												  Source="{Binding Reviews}">
										<DataTemplate>
											<muxc:ItemsRepeater ItemsSource="{Binding Data}">
												<muxc:ItemsRepeater.Layout>
													<muxc:StackLayout Orientation="Vertical"
																	  Spacing="2" />
												</muxc:ItemsRepeater.Layout>
												<muxc:ItemsRepeater.ItemTemplate>
													<DataTemplate>
														<utu:AutoLayout Background="{ThemeResource SurfaceBrush}"
																		Spacing="16"
																		CornerRadius="4"
																		Orientation="Horizontal"
																		Padding="16">
															<PersonPicture Width="60"
																		   Height="60"
																		   ProfilePicture="{Binding UrlAuthorImage}" />
															<utu:AutoLayout Spacing="8"
																			utu:AutoLayout.PrimaryAlignment="Stretch"
																			PrimaryAxisAlignment="Center">
																<TextBlock TextWrapping="Wrap"
																		   Text="{Binding Description}"
																		   Foreground="{ThemeResource OnSurfaceBrush}" />
																<TextBlock TextWrapping="Wrap"
																		   Text="{Binding PublisherName}"
																		   Foreground="{ThemeResource OnSurfaceMediumBrush}" />
																<utu:AutoLayout Spacing="{utu:Responsive Normal=8,
																										 Wide=24}"
																				Orientation="Horizontal"
																				CounterAxisAlignment="Center"
																				Padding="{utu:Responsive Normal='0,16,0,16',
																										 Wide='0,16,0,0'}">
																	<ToggleButton HorizontalContentAlignment="Center"
																				  VerticalContentAlignment="Center"
																				  Command="{utu:AncestorBinding AncestorType=uer:FeedView,
																												Path=DataContext.Like}"
																				  CommandParameter="{Binding}"
																				  IsChecked="{Binding UserLike, Converter={StaticResource UserLikeCheckedConverter}, Mode=TwoWay}">
																		<ToggleButton.Content>
																			<utu:AutoLayout Orientation="Horizontal"
																							Spacing="{utu:Responsive Normal=5,
																													 Wide=8}"
																							CounterAxisAlignment="Center">
																				<PathIcon Data="{StaticResource Icon_Thumb_Up_Off}"
																						  Foreground="{ThemeResource PrimaryBrush}" />
																				<TextBlock Text="{Binding Likes.Count}"
																						   Foreground="{ThemeResource PrimaryBrush}"
																						   Style="{StaticResource LabelLarge}" />
																			</utu:AutoLayout>
																		</ToggleButton.Content>
																		<ut:ControlExtensions.AlternateContent>
																			<utu:AutoLayout Orientation="Horizontal"
																							Spacing="{utu:Responsive Normal=5,
																													 Wide=8}"
																							CounterAxisAlignment="Center">
																				<PathIcon Data="{StaticResource Icon_Thumb_Up}"
																						  Foreground="{ThemeResource PrimaryBrush}" />
																				<TextBlock Text="{Binding Likes.Count}"
																						   Foreground="{ThemeResource PrimaryBrush}"
																						   Style="{StaticResource LabelLarge}" />
																			</utu:AutoLayout>
																		</ut:ControlExtensions.AlternateContent>
																	</ToggleButton>
																	<ToggleButton HorizontalContentAlignment="Center"
																				  VerticalContentAlignment="Center"
																				  Command="{utu:AncestorBinding AncestorType=uer:FeedView,
																												Path=DataContext.Dislike}"
																				  CommandParameter="{Binding}"
																				  IsChecked="{Binding UserLike, Converter={StaticResource UserDislikeCheckedConverter}, Mode=TwoWay}">
																		<ToggleButton.Content>
																			<utu:AutoLayout Orientation="Horizontal"
																							Spacing="{utu:Responsive Normal=5,
																													 Wide=8}"
																							CounterAxisAlignment="Center">
																				<PathIcon Data="{StaticResource Icon_Thumb_Down_Off}"
																						  Foreground="{ThemeResource PrimaryBrush}" />
																				<TextBlock Text="{Binding Dislikes.Count}"
																						   Foreground="{ThemeResource PrimaryBrush}"
																						   Style="{StaticResource LabelLarge}" />
																			</utu:AutoLayout>
																		</ToggleButton.Content>
																		<ut:ControlExtensions.AlternateContent>
																			<utu:AutoLayout Orientation="Horizontal"
																							Spacing="8"
																							CounterAxisAlignment="Center">
																				<PathIcon Data="{StaticResource Icon_Thumb_Down}"
																						  Foreground="{ThemeResource PrimaryBrush}" />
																				<TextBlock Text="{Binding Dislikes.Count}"
																						   Foreground="{ThemeResource PrimaryBrush}"
																						   Style="{StaticResource LabelLarge}" />
																			</utu:AutoLayout>
																		</ut:ControlExtensions.AlternateContent>
																	</ToggleButton>
																</utu:AutoLayout>
															</utu:AutoLayout>
														</utu:AutoLayout>
													</DataTemplate>
												</muxc:ItemsRepeater.ItemTemplate>
											</muxc:ItemsRepeater>
										</DataTemplate>
									</uer:FeedView>
								</utu:AutoLayout>

								<Grid uen:Region.Name="NutritionTab"
									  Visibility="Collapsed">
									<Grid Margin="{utu:Responsive Normal=16,
																  Wide=40}"
										  Padding="{utu:Responsive Normal=16,
																   Wide=24}"
										  Background="{ThemeResource SurfaceBrush}"
										  CornerRadius="24"
										  RowSpacing="{utu:Responsive Normal=5,
																	  Wide=24}">
										<Grid.RowDefinitions>
											<RowDefinition Height="Auto" />
											<RowDefinition Height="Auto" />
										</Grid.RowDefinitions>
										<TextBlock TextAlignment="Center"
												   Text="Intake per serving"
												   Foreground="{ThemeResource OnBackgroundBrush}"
												   Style="{StaticResource TitleLarge}" />
										<ctrl:ChartControl DataContext="{Binding Recipe}"
														   CarbBrush="{ThemeResource NutritionCarbsValBrush}"
														   ProteinBrush="{ThemeResource NutritionProteinValBrush}"
														   FatBrush="{ThemeResource NutritionFatValBrush}"
														   DataLabelBrush="{ThemeResource NutritionDataLabelBrush}"
														   TrackBackgroundBrush="{ThemeResource NutritionTrackBackgroundBrush}"
														   Grid.Row="1" />
									</Grid>
								</Grid>
							</Grid>
						</ScrollViewer>
					</utu:AutoLayout>
				</utu:AutoLayout>
			</utu:AutoLayout>
		</ScrollViewer>

		<Button Content="Start Cooking!"
				Style="{StaticResource ChefsFabButtonStyle}"
				Command="{Binding LiveCooking}"
				VerticalAlignment="Bottom"
				HorizontalAlignment="Right"
				utu:AutoLayout.IsIndependentLayout="True">
			<ut:ControlExtensions.Icon>
				<FontIcon Style="{StaticResource FontAwesomeSolidFontIconStyle}"
						  Glyph="{StaticResource Icon_Carrot}" />
			</ut:ControlExtensions.Icon>
		</Button>
	</utu:AutoLayout>
</Page>
