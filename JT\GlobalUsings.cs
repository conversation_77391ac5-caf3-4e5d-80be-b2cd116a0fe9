global using System;
global using System.Collections.Generic;
global using System.Collections.Immutable;
global using System.Linq;
global using System.Net.Http;
global using System.Threading;
global using System.Threading.Tasks;
global using System.Windows.Input;
global using JT.Business.Models;
global using JT.Business.Services;
global using JT.Business.Services.Notifications;
global using JT.Business.Services.Users;
global using JT.Business.Services.TransferRequests;
global using JT.Business.Services.Subscriptions;
global using JT.Business.Services.Tokens;
global using JT.Business.Services.Skills;
global using JT.Content.Client.Mock;
global using JT.Content.Client.Models;
global using JT.Presentation;
global using CommunityToolkit.Mvvm.ComponentModel;
global using CommunityToolkit.Mvvm.Messaging;
global using Microsoft.Extensions.DependencyInjection;
global using Microsoft.Extensions.Hosting;
global using Microsoft.Extensions.Localization;
global using Microsoft.Extensions.Logging;
global using Microsoft.Extensions.Options;
global using Uno.Extensions.Http.Kiota;
global using Microsoft.UI.Xaml;
global using Microsoft.UI.Xaml.Controls;
global using Uno.Extensions;
global using Uno.Extensions.Configuration;
global using Uno.Extensions.Hosting;
global using Uno.Extensions.Localization;
global using Uno.Extensions.Logging;
global using Uno.Extensions.Navigation;
global using Uno.Extensions.Reactive;
global using Uno.Extensions.Reactive.Messaging;
global using Uno.Extensions.Serialization;
global using Uno.Extensions.Storage;
global using Uno.Toolkit.UI;
global using Application = Microsoft.UI.Xaml.Application;
global using Notification = JT.Business.Models.Notification;
global using ApplicationExecutionState = Windows.ApplicationModel.Activation.ApplicationExecutionState;
[assembly: Uno.Extensions.Reactive.Config.BindableGenerationTool(3)]
